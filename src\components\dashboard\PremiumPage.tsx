import React, { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import Button from '../common/Button';
import { Save } from 'lucide-react';

const PremiumAnalysisForm = () => {
  // Initialize form state for new scenarios
  const [formData, setFormData] = useState(() => {
    // Extract premium amount from policy data if available
    let initialPremium = '';
    if (selectedPolicyData?.premium) {
      const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
      initialPremium = premiumMatch ? premiumMatch[1] : '';
    }

    // Calculate current age if customer data is available
    let initialAge = '40';
    if (selectedCustomerData?.details?.DOB) {
      const [day, month, year] = selectedCustomerData.details.DOB.split('.').map(Number);
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      initialAge = age.toString();
    }

    return {
      // Scenario 1: Annual Premium Modification
      currentPremium: initialPremium,
      changeToNewAmount: false,
      newPremiumAmount: '',
      modifyPremiumStartingAge: false,
      modifyPremiumAge: '',
      lumpSumPremium: false,
      lumpSumAmount: '',

      // Scenario 2: Premium Payment Cessation
      stopPremiumNow: false,
      stopPremiumAge: initialAge,
      modifyStopPremium: false,
      modifyStopPremiumAge: '',

      // Scenario 3: Cash Value Target Premium Calculation
      cashValueTarget: ''
    };
  });

  // Dashboard context for tab and customer selection
  const { setActiveTab, selectedCustomerData, selectedPolicyData } = useDashboard();

  // Analysis results state
  type ProjectionData = {
    year: number;
    basePremium: number;
    modifiedPremium: number;
    baseCashValue: number;
    modifiedCashValue: number;
  };
  const [analysisResults, setAnalysisResults] = useState<ProjectionData[] | null>(null);
  const [showReport, setShowReport] = useState(false);

  // Handle form input changes
  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle option selection for scenario 1
  const handleScenario1Option = (option: string) => {
    setFormData(prev => ({
      ...prev,
      changeToNewAmount: option === 'changeAmount',
      modifyPremiumStartingAge: option === 'modifyAge',
      lumpSumPremium: option === 'lumpSum'
    }));
  };

  // Handle option selection for scenario 2
  const handleScenario2Option = (option: string) => {
    setFormData(prev => ({
      ...prev,
      stopPremiumNow: option === 'now',
      modifyStopPremium: option === 'modify'
    }));
  };

  // Generate sample projection data
  const generateProjectionData = () => {
    const years = Array.from({ length: 20 }, (_, i) => i + 1);
    const currentPremium = parseFloat(formData.currentPremium) || 5000;

    return years.map(year => {
      let basePremium = currentPremium;
      let modifiedPremium = currentPremium;
      let baseCashValue = currentPremium * year * 0.8;
      let modifiedCashValue = currentPremium * year * 0.8;

      // Apply new premium amount
      if (formData.changeToNewAmount && formData.newPremiumAmount) {
        const newPremium = parseFloat(formData.newPremiumAmount);
        modifiedPremium = newPremium;
        modifiedCashValue = baseCashValue + (newPremium - currentPremium) * year * 0.8;
      }

      // Apply modify premium starting from age
      if (formData.modifyPremiumStartingAge && formData.modifyPremiumAge && formData.newPremiumAmount) {
        const newPremium = parseFloat(formData.newPremiumAmount);
        const startAge = parseInt(formData.modifyPremiumAge) || 1;
        const currentAge = 40; // Assume current age
        const startYear = Math.max(1, startAge - currentAge + 1);

        if (year >= startYear) {
          modifiedPremium = newPremium;
          modifiedCashValue = baseCashValue + (newPremium - currentPremium) * (year - startYear + 1) * 0.8;
        }
      }

      // Apply lump sum premium
      if (formData.lumpSumPremium && formData.lumpSumAmount) {
        const lumpSum = parseFloat(formData.lumpSumAmount);
        if (year === 1) {
          modifiedPremium = lumpSum;
        } else {
          modifiedPremium = 0;
        }
        modifiedCashValue = lumpSum * Math.pow(1.04, year - 1);
      }

      // Apply stop premium payments
      if (formData.stopPremiumNow) {
        modifiedPremium = 0;
        modifiedCashValue = baseCashValue * 0.95; // Assume some decline without premiums
      } else if (formData.modifyStopPremium && formData.modifyStopPremiumAge) {
        const stopAge = parseInt(formData.modifyStopPremiumAge);
        const currentAge = 40; // Assume current age
        const stopYear = stopAge - currentAge + 1;
        if (year >= stopYear) {
          modifiedPremium = 0;
          modifiedCashValue = baseCashValue * 0.95; // Assume some decline without premiums
        }
      }

      // Apply cash value target adjustments
      if (formData.cashValueTarget) {
        const target = parseFloat(formData.cashValueTarget);
        const targetYear = 10; // Assume target in 10 years
        if (year <= targetYear) {
          const requiredGrowth = target / targetYear;
          modifiedCashValue = Math.max(modifiedCashValue, requiredGrowth * year);
        }
      }

      return {
        year,
        basePremium,
        modifiedPremium,
        baseCashValue,
        modifiedCashValue
      };
    });
  };

  // Run analysis
  const runAnalysis = () => {
    const projectionData = generateProjectionData();
    setAnalysisResults(projectionData);
  };

  // Generate report
  const generateReport = () => {
    runAnalysis();
    setShowReport(true);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      // Scenario 1: Annual Premium Modification
      currentPremium: '',
      changeToNewAmount: false,
      newPremiumAmount: '',
      modifyPremiumStartingAge: false,
      modifyPremiumAge: '',
      lumpSumPremium: false,
      lumpSumAmount: '',

      // Scenario 2: Premium Payment Cessation
      stopPremiumNow: false,
      stopPremiumAge: '',
      modifyStopPremium: false,
      modifyStopPremiumAge: '',

      // Scenario 3: Cash Value Target Premium Calculation
      cashValueTarget: ''
    });
    setAnalysisResults(null);
    setShowReport(false);
  };

  // --- Customer Info Extraction (similar to AsIsPage) ---
  let customerInfo = null;
  if (selectedCustomerData && selectedPolicyData) {
    // Extract premium amount from string (e.g., "2000 $ annually" -> "2000")
    const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
    const premiumAmount = premiumMatch ? premiumMatch[1] : '';
    // Extract coverage amount from string (e.g., "500,000 $" -> "500000")
    const coverageMatch = selectedPolicyData.coverage.replace(/,/g, '').match(/(\d+)/);
    const coverageAmount = coverageMatch ? coverageMatch[1] : '';
    // Calculate current age from DOB (assuming DD.MM.YYYY)
    const calculateAge = (dobString: string): string => {
      const [day, month, year] = dobString.split('.').map(Number);
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age.toString();
    };
    customerInfo = {
      policyNumber: selectedCustomerData.details["Policy Number"] || selectedCustomerData.policyNumber,
      customerName: selectedCustomerData.name,
      customerId: selectedCustomerData.details["Customer ID"] || selectedCustomerData.customerId,
      policyType: selectedPolicyData.name,
      faceAmount: coverageAmount,
      annualPremium: premiumAmount,
      paymentPeriod: '20', // Default or extract if available
      dividendOption: 'Paid-up Additions', // Default or extract if available
      currentAge: calculateAge(selectedCustomerData.details.DOB),
      retirementAge: '65',
      lifeExpectancy: '85',
    };
  }

  // --- Render ---
  return (
    <div className="w-full max-w-none space-y-6 px-4 py-4">
      {/* Introduction Text */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <p className="text-lg text-gray-800 leading-relaxed">
          You may want to change the annual premium for making, or lower outgo, single payment, or to
          increase to accrue more cash value, shorten the payment period or pay catch-up premiums to restore
          or strengthen your policy's performance or model level premium. Your scenarios will be displayed for
          Current Interest Rate.
        </p>
      </div>





      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Premium illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="space-y-8">
          {/* Scenario 1: Annual Premium Modification */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">1. Annual Premium Modification</h3>

            <div className="space-y-6">
              {/* Question with auto-populated amount */}
              <div className="mb-6">
                <p className="text-lg font-semibold text-black mb-4">
                  Your current annual premium is ${customerInfo?.annualPremium || formData.currentPremium || '_______'}. Do you want to change it?
                </p>
              </div>

              {/* Options */}
              <div className="space-y-4">
                {/* Option A: Change to New premium amount */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.changeToNewAmount}
                      onChange={(e) => handleScenario1Option(e.target.checked ? 'changeAmount' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Change to New premium amount?</span>
                      {formData.changeToNewAmount && (
                        <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                          <div className="flex items-center space-x-2">
                            <span className="text-black">$</span>
                            <input
                              type="text"
                              value={formData.newPremiumAmount}
                              onChange={(e) => handleInputChange('newPremiumAmount', e.target.value)}
                              placeholder="Enter new premium amount"
                              className="w-48 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </label>
                </div>

                {/* Option B: Modify the premium starting from age */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.modifyPremiumStartingAge}
                      onChange={(e) => handleScenario1Option(e.target.checked ? 'modifyAge' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Modify the premium starting from age?</span>
                      {formData.modifyPremiumStartingAge && (
                        <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-black">New premium amount: $</span>
                              <input
                                type="text"
                                value={formData.newPremiumAmount}
                                onChange={(e) => handleInputChange('newPremiumAmount', e.target.value)}
                                placeholder="Enter amount"
                                className="w-32 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                              />
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-black">Starting from age:</span>
                              <select
                                value={formData.modifyPremiumAge}
                                onChange={(e) => handleInputChange('modifyPremiumAge', e.target.value)}
                                className="px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                              >
                                <option value="">Select Age</option>
                                {Array.from({ length: 50 }, (_, i) => i + 25).map(age => (
                                  <option key={age} value={age}>{age}</option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </label>
                </div>

                {/* Option C: Lump Sum */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.lumpSumPremium}
                      onChange={(e) => handleScenario1Option(e.target.checked ? 'lumpSum' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Lump Sum (One-time premium) now?</span>
                      {formData.lumpSumPremium && (
                        <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                          <div className="flex items-center space-x-2">
                            <span className="text-black">$</span>
                            <input
                              type="text"
                              value={formData.lumpSumAmount}
                              onChange={(e) => handleInputChange('lumpSumAmount', e.target.value)}
                              placeholder="Enter lump sum amount"
                              className="w-48 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Scenario 2: Premium Payment Cessation */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">2. Premium Payment Cessation</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <p className="text-lg font-semibold text-black mb-4">
                  Do you want to stop paying future premiums and see how long the policy remains in force? Or Do you want to model policy lapse age/year by stopping premiums?
                </p>

                <div className="space-y-4">
                  {/* Option A: Now */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={formData.stopPremiumNow}
                        onChange={(e) => handleScenario2Option(e.target.checked ? 'now' : '')}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>Now</span>
                        {formData.stopPremiumNow && (
                          <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                            <div className="space-y-4">
                              <label className="block text-sm font-bold text-black mb-2">Stop Premium Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => {
                                    const currentAge = parseInt(formData.stopPremiumAge) || (customerInfo?.currentAge ? parseInt(customerInfo.currentAge) : 40);
                                    if (currentAge > 25) {
                                      handleInputChange('stopPremiumAge', (currentAge - 1).toString());
                                    }
                                  }}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {formData.stopPremiumAge || customerInfo?.currentAge || '40'}
                                </span>
                                <button
                                  onClick={() => {
                                    const currentAge = parseInt(formData.stopPremiumAge) || (customerInfo?.currentAge ? parseInt(customerInfo.currentAge) : 40);
                                    if (currentAge < 100) {
                                      handleInputChange('stopPremiumAge', (currentAge + 1).toString());
                                    }
                                  }}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </label>
                  </div>

                  {/* Option B: Modify the */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={formData.modifyStopPremium}
                        onChange={(e) => handleScenario2Option(e.target.checked ? 'modify' : '')}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>Modify the</span>
                        {formData.modifyStopPremium && (
                          <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                            <div className="flex items-center space-x-2">
                              <span className="text-black">Stop premium starting from age:</span>
                              <select
                                value={formData.modifyStopPremiumAge}
                                onChange={(e) => handleInputChange('modifyStopPremiumAge', e.target.value)}
                                className="px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                              >
                                <option value="">Select Age</option>
                                {Array.from({ length: 50 }, (_, i) => i + 25).map(age => (
                                  <option key={age} value={age}>{age}</option>
                                ))}
                              </select>
                            </div>
                          </div>
                        )}
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Scenario 3: Cash Value Target Premium Calculation */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">3. Cash Value Target Premium Calculation</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <p className="text-lg font-semibold text-black mb-4">
                  Calculate the new premium schedule, for a cash value target of ______ from now.
                </p>

                <div className="space-y-4">
                  <div className="pl-4">
                    <div className="bg-gray-50 p-4 rounded-lg border">
                      <div className="flex items-center space-x-2">
                        <span className="text-black font-semibold">Cash Value Target: $</span>
                        <input
                          type="text"
                          value={formData.cashValueTarget}
                          onChange={(e) => handleInputChange('cashValueTarget', e.target.value)}
                          placeholder="Enter target amount"
                          className="w-48 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button
              onClick={() => alert('Analysis saved to history!')}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Premium Analysis</span>
            </Button>
            <Button
              onClick={resetForm}
              variant="primary"
              className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white shadow-lg border-none"
            >
              <span>Reset</span>
            </Button>
          </div>
        </div>
      )}

      {/* Analysis Results */}
      {analysisResults && (
        <div className="mt-8 bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl shadow-lg border-l-4 border-green-500">
          <h3 className="text-xl font-bold text-green-800 mb-6 text-center">📊 Premium Analysis Results</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Premium Projection Chart */}
            <div>
              <h4 className="text-lg font-bold text-black mb-4">Premium Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="basePremium" stroke="#2563eb" strokeWidth={2} name="Current Premium" />
                  <Line type="monotone" dataKey="modifiedPremium" stroke="#dc2626" strokeWidth={2} name="Modified Premium" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Cash Value Projection Chart */}
            <div>
              <h4 className="text-lg font-bold text-black mb-4">Cash Value Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="baseCashValue" stroke="#16a34a" strokeWidth={2} name="Current Cash Value" />
                  <Line type="monotone" dataKey="modifiedCashValue" stroke="#ea580c" strokeWidth={2} name="Modified Cash Value" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Analysis Summary */}
          <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
            <h4 className="text-lg font-bold text-black mb-4">Analysis Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(formData.currentPremium || customerInfo?.annualPremium) && (
                <div className="text-black">
                  <strong>Current Annual Premium:</strong> ${formData.currentPremium || customerInfo?.annualPremium}
                </div>
              )}
              {formData.changeToNewAmount && formData.newPremiumAmount && (
                <div className="text-black">
                  <strong>New Premium Amount:</strong> ${formData.newPremiumAmount}
                </div>
              )}
              {formData.modifyPremiumStartingAge && formData.newPremiumAmount && formData.modifyPremiumAge && (
                <div className="text-black">
                  <strong>Modified Premium:</strong> ${formData.newPremiumAmount} starting from age {formData.modifyPremiumAge}
                </div>
              )}
              {formData.lumpSumPremium && formData.lumpSumAmount && (
                <div className="text-black">
                  <strong>Lump Sum Premium:</strong> ${formData.lumpSumAmount}
                </div>
              )}
              {formData.stopPremiumNow && (
                <div className="text-black">
                  <strong>Stop Premium:</strong> Now (Age {formData.stopPremiumAge || customerInfo?.currentAge || '40'})
                </div>
              )}
              {formData.modifyStopPremium && formData.modifyStopPremiumAge && (
                <div className="text-black">
                  <strong>Stop Premium at Age:</strong> {formData.modifyStopPremiumAge}
                </div>
              )}
              {formData.cashValueTarget && (
                <div className="text-black">
                  <strong>Cash Value Target:</strong> ${formData.cashValueTarget}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive Report */}
      {showReport && (
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
          <h3 className="text-xl font-bold text-blue-800 mb-6 text-center">📈 Comprehensive Premium Analysis Report</h3>
          
          <div className="space-y-6">
            {/* Report Header */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Report Details</h4>
              <p><strong>Report Generated:</strong> {new Date().toLocaleString()}</p>
              <p><strong>Customer:</strong> {selectedCustomerData?.name || 'N/A'}</p>
              <p><strong>Policy Number:</strong> {selectedCustomerData?.details?.["Policy Number"] || selectedCustomerData?.policyNumber || 'N/A'}</p>
              <p><strong>Policy Type:</strong> {selectedPolicyData?.name || 'N/A'}</p>
            </div>

            {/* Executive Summary */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Executive Summary</h4>
              <ul className="space-y-2 text-black">
                {(formData.currentPremium || customerInfo?.annualPremium) && <li>• Current annual premium: ${formData.currentPremium || customerInfo?.annualPremium}</li>}
                {formData.changeToNewAmount && formData.newPremiumAmount && <li>• New premium amount: ${formData.newPremiumAmount}</li>}
                {formData.modifyPremiumStartingAge && formData.newPremiumAmount && formData.modifyPremiumAge && <li>• Modified premium: ${formData.newPremiumAmount} starting from age ${formData.modifyPremiumAge}</li>}
                {formData.lumpSumPremium && formData.lumpSumAmount && <li>• Lump sum premium: ${formData.lumpSumAmount}</li>}
                {formData.stopPremiumNow && <li>• Analysis includes immediate premium payment cessation</li>}
                {formData.modifyStopPremium && formData.modifyStopPremiumAge && <li>• Analysis includes premium payment cessation from age ${formData.modifyStopPremiumAge}</li>}
                {formData.cashValueTarget && <li>• Cash value target: ${formData.cashValueTarget}</li>}
              </ul>
            </div>

            {/* Recommendations */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Recommendations</h4>
              <ul className="space-y-2 text-black">
                {(formData.stopPremiumNow || formData.modifyStopPremium) && <li>• Consider the policy risk when stopping premium payments</li>}
                {formData.lumpSumPremium && <li>• Lump sum option eliminates future premium payment uncertainty</li>}
                {(formData.changeToNewAmount || formData.modifyPremiumStartingAge) && <li>• Premium adjustments can help optimize policy performance</li>}
                {formData.cashValueTarget && <li>• Setting cash value targets helps achieve financial goals</li>}
                <li>• Regular policy reviews are recommended to monitor performance</li>
                <li>• Consider current interest rate environment when making premium decisions</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PremiumAnalysisForm;